/*
 See the LICENSE.txt file for this sample’s licensing information.
 
 Abstract:
 The sample data.
 */

import Foundation

struct SampleData {
    static let sectionsStore = AnyModelStore([
        Section(id: .featured, posts: ["a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"]),
        Section(id: .all, posts: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22"])
    ])
    
    static let postsStore = AnyModelStore([
        DestinationPost(id: "a", region: "Peru", subregion: "Cusco", numberOfLikes: 31, assetID: "cusco"),
        DestinationPost(id: "b", region: "Caribbean", subregion: "Saint Lucia", numberOfLikes: 25, assetID: "st-lucia"),
        DestinationPost(id: "c", region: "Japan", subregion: "Tokyo", numberOfLikes: 16, assetID: "tokyo"),
        DestinationPost(id: "d", region: "Iceland", subregion: "Reykjavík", numberOfLikes: 9, assetID: "iceland"),
        DestinationPost(id: "e", region: "France", subregion: "Paris", numberOfLikes: 14, assetID: "paris"),
        DestinationPost(id: "f", region: "Italy", subregion: "Capri", numberOfLikes: 11, assetID: "italy"),
        DestinationPost(id: "g", region: "Viet Nam", subregion: "Cat Ba", numberOfLikes: 17, assetID: "vietnam"),
        DestinationPost(id: "h", region: "New Zealand", subregion: nil, numberOfLikes: 5, assetID: "new-zealand"),
        DestinationPost(id: "i", region: "Indonesia", subregion: "Bali", numberOfLikes: 10, assetID: "bali"),
        DestinationPost(id: "j", region: "Ireland", subregion: "Cork", numberOfLikes: 21, assetID: "cork"),
        DestinationPost(id: "k", region: "Chile", subregion: "Patagonia", numberOfLikes: 9, assetID: "patagonia"),
        DestinationPost(id: "l", region: "Cambodia", subregion: nil, numberOfLikes: 14, assetID: "cambodia"),
        DestinationPost(id: "m", region: "Peru", subregion: "Cusco", numberOfLikes: 31, assetID: "cusco"),
        DestinationPost(id: "n", region: "Caribbean", subregion: "Saint Lucia", numberOfLikes: 25, assetID: "st-lucia"),
        DestinationPost(id: "o", region: "Japan", subregion: "Tokyo", numberOfLikes: 16, assetID: "tokyo"),
        DestinationPost(id: "p", region: "Iceland", subregion: "Reykjavík", numberOfLikes: 9, assetID: "iceland"),
        DestinationPost(id: "q", region: "France", subregion: "Paris", numberOfLikes: 14, assetID: "paris"),
        DestinationPost(id: "r", region: "Italy", subregion: "Capri", numberOfLikes: 11, assetID: "italy"),
        DestinationPost(id: "s", region: "Viet Nam", subregion: "Cat Ba", numberOfLikes: 17, assetID: "vietnam"),
        DestinationPost(id: "t", region: "New Zealand", subregion: nil, numberOfLikes: 5, assetID: "new-zealand"),
        DestinationPost(id: "u", region: "Indonesia", subregion: "Bali", numberOfLikes: 10, assetID: "bali"),
        DestinationPost(id: "v", region: "Ireland", subregion: "Cork", numberOfLikes: 21, assetID: "cork"),
        DestinationPost(id: "w", region: "Chile", subregion: "Patagonia", numberOfLikes: 9, assetID: "patagonia"),
        DestinationPost(id: "x", region: "Cambodia", subregion: nil, numberOfLikes: 14, assetID: "cambodia"),
        DestinationPost(id: "y", region: "Peru", subregion: "Cusco", numberOfLikes: 31, assetID: "cusco"),
        DestinationPost(id: "z", region: "Caribbean", subregion: "Saint Lucia", numberOfLikes: 25, assetID: "st-lucia"),
        
        DestinationPost(id: "1", region: "Japan", subregion: "Tokyo", numberOfLikes: 16, assetID: "tokyo"),
        DestinationPost(id: "2", region: "Iceland", subregion: "Reykjavík", numberOfLikes: 9, assetID: "iceland"),
        DestinationPost(id: "3", region: "France", subregion: "Paris", numberOfLikes: 14, assetID: "paris"),
        DestinationPost(id: "4", region: "Italy", subregion: "Capri", numberOfLikes: 11, assetID: "italy"),
        DestinationPost(id: "5", region: "Viet Nam", subregion: "Cat Ba", numberOfLikes: 17, assetID: "vietnam"),
        DestinationPost(id: "6", region: "New Zealand", subregion: nil, numberOfLikes: 5, assetID: "new-zealand"),
        DestinationPost(id: "7", region: "Indonesia", subregion: "Bali", numberOfLikes: 10, assetID: "bali"),
        DestinationPost(id: "8", region: "Ireland", subregion: "Cork", numberOfLikes: 21, assetID: "cork"),
        DestinationPost(id: "9", region: "Chile", subregion: "Patagonia", numberOfLikes: 9, assetID: "patagonia"),
        DestinationPost(id: "10", region: "Cambodia", subregion: nil, numberOfLikes: 14, assetID: "cambodia"),
        DestinationPost(id: "11", region: "Peru", subregion: "Cusco", numberOfLikes: 31, assetID: "cusco"),
        DestinationPost(id: "12", region: "Caribbean", subregion: "Saint Lucia", numberOfLikes: 25, assetID: "st-lucia"),
        DestinationPost(id: "13", region: "Japan", subregion: "Tokyo", numberOfLikes: 16, assetID: "tokyo"),
        DestinationPost(id: "14", region: "Iceland", subregion: "Reykjavík", numberOfLikes: 9, assetID: "iceland"),
        DestinationPost(id: "15", region: "France", subregion: "Paris", numberOfLikes: 14, assetID: "paris"),
        DestinationPost(id: "16", region: "Italy", subregion: "Capri", numberOfLikes: 11, assetID: "italy"),
        DestinationPost(id: "17", region: "Viet Nam", subregion: "Cat Ba", numberOfLikes: 17, assetID: "vietnam"),
        DestinationPost(id: "18", region: "New Zealand", subregion: nil, numberOfLikes: 5, assetID: "new-zealand"),
        DestinationPost(id: "19", region: "Indonesia", subregion: "Bali", numberOfLikes: 10, assetID: "bali"),
        DestinationPost(id: "20", region: "Ireland", subregion: "Cork", numberOfLikes: 21, assetID: "cork"),
        DestinationPost(id: "21", region: "Chile", subregion: "Patagonia", numberOfLikes: 9, assetID: "patagonia"),
        DestinationPost(id: "22", region: "Cambodia", subregion: nil, numberOfLikes: 14, assetID: "cambodia")
    ])
    
    static let assetsStore = AssetStore()
}
